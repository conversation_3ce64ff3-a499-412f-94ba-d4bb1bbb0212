{"name": "vue-project", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build-sandbox": "run-p type-check \"build-only --mode development\" && node ./upload/index", "build-prod": "run-p type-check \"build-only --mode production\" && node ./upload/index", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.7.7", "md5": "^2.3.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^4.0.2", "platform": "^1.3.6", "swiper": "^11.2.8", "vue": "^3.4.29", "vue-i18n": "^11.1.5", "vue-router": "^4.3.3"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/md5": "^2.3.5", "@types/node": "^20.14.5", "@types/platform": "^1.3.6", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "basic-ftp": "^5.0.5", "commander": "^12.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "less": "^4.2.0", "npm-run-all2": "^6.2.0", "prettier": "^3.2.5", "sharp": "^0.34.1", "typescript": "~5.4.0", "vite": "^5.3.1", "vue-tsc": "^2.0.21"}}