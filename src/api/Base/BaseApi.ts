import axios from "axios";
import type {
  AxiosRequestConfig,
  AxiosRequestHeaders,
  InternalAxiosRequestConfig,
} from "axios"; // 使用 type 关键字导入类型
import type { IApiResult } from "../viewModel/IApiResult";
import { useAppStore } from "@/stores/app";

interface RequestConfig extends AxiosRequestConfig {
  params?: Record<string, any>;
  headers?: AxiosRequestHeaders; // Update the headers type to AxiosRequestHeaders
}

// 全局加载状态
// export const isLoading = ref(false)
let requestCount = 0; // 追踪进行中的请求数

// 显示加载动画
function showLoading() {
  if (requestCount === 0) {
    useAppStore().isShowLoadding = true;
  }
  requestCount++;
}

// 隐藏加载动画
function hideLoading() {
  requestCount--;
  if (requestCount === 0) {
    useAppStore().isShowLoadding = false;
  }
}

// 添加请求拦截器
axios.interceptors.request.use(
  (config: RequestConfig) => {
    // 显示加载动画
    // showLoading();
    config.baseURL = import.meta.env.VITE_API_URL;
    if (config.method?.toLowerCase() === "get") {
      config.params = {
        ...config.params,
      };
    } else if (config.method?.toLowerCase() === "post") {
      config.data = {
        ...config.data,
      };
    }

    return config as InternalAxiosRequestConfig<any>;
  },
  (error) => {
    // 请求错误时隐藏加载动画
    // hideLoading();
    return Promise.reject(error);
  }
);

// 添加响应拦截器
axios.interceptors.response.use(
  (response) => {
    // hideLoading();
    if (response.data.code === 403) {
      // PathsHelper.ToLogin()
    }
    return response;
  },
  async (err) => {
    // hideLoading();
    return Promise.reject(err);
  }
);

export class BaseApi {
  /**
   * 标准响应(IApiResult)的get请求用这个
   * @param uri 请求链接
   * @param params 参数
   */
  protected static async get<T>(
    uri: string,
    params: object = {},
    config?: object
  ): Promise<IApiResult<T>> {
    return axios
      .get<IApiResult<T>>(uri, { params, ...config })
      .then((res) => res.data)
      .catch((err) => {
        const errResult: IApiResult<T> = {
          code: err.response?.status || 0,
          message: err.message,
        };
        return errResult;
      });
  }

  /**
   * 标准响应(IApiResult)的post请求用这个
   * @param uri 请求链接
   * @param data 参数
   */
  protected static async post<T>(
    uri: string,
    data: object = {},
    config?: object
  ): Promise<IApiResult<T>> {
    return axios
      .post<IApiResult<T>>(uri, data, config)
      .then((res) => res.data)
      .catch((err) => {
        const errResult: IApiResult<T> = {
          code: err.response?.status || 0,
          message: err.message,
        };
        return errResult;
      });
  }

  /**
   * 特殊响应的get请求用这个
   * @param uri 请求链接
   * @param params 参数
   */
  protected static async specialGet<T>(
    uri: string,
    params: object = {},
    config?: object
  ): Promise<T> {
    return axios.get<T>(uri, { params, ...config }).then((res) => res.data);
  }

  /**
   * 特殊响应的post请求用这个
   * @param uri 请求链接
   * @param data 参数
   */
  protected static async specialPost<T>(
    uri: string,
    data: object = {},
    config?: object
  ): Promise<T> {
    return axios.post<T>(uri, data, config).then((res) => res.data);
  }
}
