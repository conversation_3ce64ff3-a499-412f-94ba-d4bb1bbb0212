* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  overflow-x: hidden;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}
img {
  width: 100%;
  height: 100%;
  display: flex;
}

.font-bold {
  font-weight: bold;
}

input {
  text-indent: 1em;
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  box-shadow: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: 0;
  margin: 0;
  -webkit-text-size-adjust: 100%;
  &:focus {
    outline: none;
    box-shadow: none;
    border: none;
  }
  &:active {
    outline: none;
    box-shadow: none;
  }
}

input::placeholder {
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

@keyframes btn {
  0% {
    transform: scale(1) translateY(-1px);
  }
  50% {
    transform: scale(1.1) translateY(-4px);
  }
  to {
    transform: scale(1) translateY(-1px);
  }
}

.animation {
  animation-name: btn;
  animation-timing-function: ease-out;
  animation-duration: 1s;
  animation-fill-mode: both;
  animation-iteration-count: infinite;
  animation-delay: 0.1s;
}

.ios,
.gp,
.pointer,
.btn {
  cursor: pointer;
}
