<template>
  <div class="game-loading-overlay" v-if="visible">
    <div class="loading-container">
      <!-- 主要加载动画 -->
      <div class="loading-spinner">
        <div class="outer-ring"></div>
        <div class="middle-ring"></div>
        <div class="inner-ring"></div>
        <div class="center-dot"></div>
      </div>
      
      <!-- 加载文字 -->
      <div class="loading-text">
        <span class="text-glow">{{ loadingText }}</span>
        <div class="dots">
          <span class="dot">.</span>
          <span class="dot">.</span>
          <span class="dot">.</span>
        </div>
      </div>
      
      <!-- 进度条 -->
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: progress + '%' }"></div>
        <div class="progress-glow"></div>
      </div>
      
      <!-- 装饰性粒子效果 -->
      <div class="particles">
        <div class="particle" v-for="i in 20" :key="i" :style="getParticleStyle(i)"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface Props {
  visible?: boolean
  loadingText?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  loadingText: 'LOADING'
})

const progress = ref(0)
let progressInterval: ReturnType<typeof setInterval> | null = null

// 模拟进度条动画
const startProgress = () => {
  progress.value = 0
  progressInterval = setInterval(() => {
    if (progress.value < 95) {
      progress.value += Math.random() * 3 + 1
    }
  }, 100)
}

// 完成加载
const completeProgress = () => {
  if (progressInterval) {
    clearInterval(progressInterval)
    progressInterval = null
  }
  progress.value = 100
}

// 粒子样式
const getParticleStyle = (index: number) => {
  const angle = (index * 18) % 360
  const radius = 150 + Math.random() * 100
  const x = Math.cos(angle * Math.PI / 180) * radius
  const y = Math.sin(angle * Math.PI / 180) * radius
  const delay = Math.random() * 2
  const duration = 2 + Math.random() * 2
  
  return {
    left: `calc(50% + ${x}px)`,
    top: `calc(50% + ${y}px)`,
    animationDelay: `${delay}s`,
    animationDuration: `${duration}s`
  }
}

onMounted(() => {
  if (props.visible) {
    startProgress()
  }
})

onUnmounted(() => {
  if (progressInterval) {
    clearInterval(progressInterval)
  }
})

// 暴露方法给父组件
defineExpose({
  completeProgress
})
</script>

<style scoped>
.game-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  overflow: hidden;
}

.loading-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px; /* 改为px单位，保持固定间距 */
}

/* 主要加载动画 */
.loading-spinner {
  position: relative;
  width: 120px;
  height: 120px;
}

.outer-ring {
  position: absolute;
  width: 120px;
  height: 120px;
  border: 3px solid transparent;
  border-top: 3px solid #00d4ff;
  border-right: 3px solid #00d4ff;
  border-radius: 50%;
  animation: spin-outer 2s linear infinite;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.middle-ring {
  position: absolute;
  top: 15px;
  left: 15px;
  width: 90px;
  height: 90px;
  border: 2px solid transparent;
  border-bottom: 2px solid #ff6b35;
  border-left: 2px solid #ff6b35;
  border-radius: 50%;
  animation: spin-middle 1.5s linear infinite reverse;
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.5);
}

.inner-ring {
  position: absolute;
  top: 30px;
  left: 30px;
  width: 60px;
  height: 60px;
  border: 2px solid transparent;
  border-top: 2px solid #7c4dff;
  border-right: 2px solid #7c4dff;
  border-radius: 50%;
  animation: spin-inner 1s linear infinite;
  box-shadow: 0 0 10px rgba(124, 77, 255, 0.5);
}

.center-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  background: radial-gradient(circle, #ffffff 0%, #00d4ff 100%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 1s ease-in-out infinite alternate;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
}

/* 加载文字 */
.loading-text {
  display: flex;
  align-items: center;
  gap: 8px; /* 改为px单位 */
  font-family: 'Arial', sans-serif;
  font-weight: bold;
  font-size: 24px !important; /* 改为px单位，避免受根字体大小影响 */
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 3px;
  transform: scale(1) !important;
  will-change: auto;
  /* 强制锁定字体大小和渲染 */
  min-font-size: 24px;
  max-font-size: 24px;
  font-size-adjust: none;
  /* 防止浏览器字体优化导致的大小变化 */
  -webkit-text-size-adjust: none;
  -moz-text-size-adjust: none;
  text-size-adjust: none;
  /* 强制使用像素渲染 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 防止任何形式的缩放 */
  zoom: 1;
  /* 锁定盒模型 */
  box-sizing: border-box;
}

.text-glow {
  text-shadow: 
    0 0 5px #00d4ff,
    0 0 10px #00d4ff,
    0 0 15px #00d4ff,
    0 0 20px #00d4ff;
  /* 移除动画以防止文字大小变化 */
  /* animation: text-flicker 2s ease-in-out infinite alternate; */
  /* 强制禁用所有动画和过渡 */
  animation: none !important;
  transition: none !important;
  transform: scale(1) !important;
}

.dots {
  display: flex;
  gap: 2px;
}

.dot {
  animation: dot-blink 1.5s ease-in-out infinite;
  color: #00d4ff;
  text-shadow: 0 0 5px #00d4ff;
}

.dot:nth-child(1) { animation-delay: 0s; }
.dot:nth-child(2) { animation-delay: 0.5s; }
.dot:nth-child(3) { animation-delay: 1s; }

/* 进度条 */
.progress-bar {
  position: relative;
  width: 300px;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00d4ff 0%, #7c4dff 50%, #ff6b35 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progress-shine 2s ease-in-out infinite;
}

.progress-glow {
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  bottom: -2px;
  background: linear-gradient(90deg, #00d4ff, #7c4dff, #ff6b35);
  border-radius: 5px;
  opacity: 0.3;
  filter: blur(4px);
  z-index: -1;
}

/* 粒子效果 */
.particles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: #00d4ff;
  border-radius: 50%;
  animation: particle-float 4s ease-in-out infinite;
  box-shadow: 0 0 6px #00d4ff;
}

.particle:nth-child(odd) {
  background: #ff6b35;
  box-shadow: 0 0 6px #ff6b35;
}

.particle:nth-child(3n) {
  background: #7c4dff;
  box-shadow: 0 0 6px #7c4dff;
}

/* 动画定义 */
@keyframes spin-outer {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes spin-middle {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(-360deg); }
}

@keyframes spin-inner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.7; }
}

@keyframes text-flicker {
  0%, 100% { 
    opacity: 1;
    transform: scale(1);
  }
  50% { 
    opacity: 0.8;
    transform: scale(1);
  }
}

@keyframes dot-blink {
  0%, 50% { opacity: 0; }
  51%, 100% { opacity: 1; }
}

@keyframes progress-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) scale(1.1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-spinner {
    width: 80px;
    height: 80px;
  }
  
  .outer-ring {
    width: 80px;
    height: 80px;
  }
  
  .middle-ring {
    top: 10px;
    left: 10px;
    width: 60px;
    height: 60px;
  }
  
  .inner-ring {
    top: 20px;
    left: 20px;
    width: 40px;
    height: 40px;
  }
  
  .loading-text {
    font-size: 19px !important; /* 改为px单位，避免受根字体大小影响 */
    letter-spacing: 2px;
  }
  
  .progress-bar {
    width: 250px;
  }
}
</style>