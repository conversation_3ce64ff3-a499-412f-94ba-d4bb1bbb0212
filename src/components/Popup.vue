<template>
  <div v-if="visible" class="popup-overlay" @click="handleOverlayClick">
    <div class="mark"></div>
    <div
      class="popup-content"
      :style="{ top: contentTop }"
      @click.stop="handlePopupClick"
    >
      <slot></slot>
      <!-- 插槽内容会在这里渲染 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, type PropType } from "vue";

// 传入的回调函数类型定义
const props = defineProps({
  contentTop: {
    type: String,
    default: "50%",
  },
  onClose: {
    type: Function as PropType<() => void>, // 回调函数类型
    default: null,
  },
});

const { contentTop, onClose } = props;

const visible = ref(false);

// 打开弹窗
const open = () => {
  visible.value = true;
};

// 关闭弹窗
const close = () => {
  visible.value = false;
  if (props.onClose) {
    props.onClose(); // 执行传递的回调函数
  }
};

// 点击遮罩层关闭弹窗
const handleOverlayClick = (e: MouseEvent) => {
  // 如果点击的是遮罩层而不是弹窗内容，则关闭弹窗
  if (e.target === e.currentTarget) {
    close();
  }
};

// 点击弹窗内容处理
const handlePopupClick = (e: MouseEvent) => {
  // 检查点击的元素是否具有 'close' 类名
  if ((e.target as HTMLElement).classList.contains("close")) {
    close(); // 如果是带 close 类的元素，关闭弹窗
  }
};

// 通过暴露的函数来控制弹窗的打开与关闭
defineExpose({ open, close });
</script>

<style scoped lang="less">
/* 设置遮罩层样式 */
.popup-overlay {
  width: 7.5rem;
  height: 100vh;
  position: fixed;
  top: 0;
  z-index: 11;
  overflow: hidden;

  .mark {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.75);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;
  }

  .popup-content {
    position: absolute;
    z-index: 1000;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

/* 针对PC端 */
@media (min-width: 541px) {
  .popup-overlay {
    width: 7.5rem;
  }
}
</style>
