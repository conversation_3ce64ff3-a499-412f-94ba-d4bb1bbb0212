<template>
  <div class="webm-player">
    <video v-if="!error" ref="videoRef" :src="src" autoplay muted loop disablepictureinpicture class="webm-video"
      @error="onError" />
    <img v-else-if="fallback" :src="fallback" class="fallback" alt="视频加载失败" />
    <div v-else class="error">视频加载失败</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Props {
  src: string
  fallback?: string
}

const props = withDefaults(defineProps<Props>(), {
})

const videoRef = ref<HTMLVideoElement>()
const error = ref(false)

const onError = () => {
  error.value = true
}

onMounted(() => {
  if (videoRef.value && props.src) {
    videoRef.value.load()
  }
})
</script>

<style scoped>
.webm-player {
  position: relative;
  display: inline-block;
}

.webm-video,
.fallback {
  width: 100%;
  height: auto;
  max-width: 100%;
}

.error {
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 4px;
  font-size: 14px;
}

.error {
  background: rgba(220, 53, 69, 0.8);
}
</style>