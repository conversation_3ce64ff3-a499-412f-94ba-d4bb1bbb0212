<template>
  <div class="horizontal-swiper-container">
    <BaseSwiper direction="horizontal" :slides-per-view="1" :space-between="0" :speed="1000"
      :autoplay="{ delay: 4000, disableOnInteraction: false }" :pagination="{ clickable: true }" :navigation="true"
      :modules="modules" :custom-class="props.fullscreen ? 'fullscreen' : props.customClass"
      @swiper-ready="onSwiperReady" @slide-change="onSlideChange">
      <slot />
    </BaseSwiper>
  </div>
</template>

<script setup lang="ts">
import BaseSwiper from './BaseSwiper.vue'
import { useSwiper } from '@/hooks/useSwiper'
import { Navigation, Pagination, Autoplay } from 'swiper/modules'

interface Props {
  customClass?: string
  fullscreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  customClass: '',
  fullscreen: false,
})

const emit = defineEmits<{
  slideChange: [index: number]
  swiperReady: [swiper: any]
}>()

// 使用基础 Swiper Hook
const {
  swiperInstance,
  currentSlide,
  isReady,
  goToSlide,
  startAutoplay,
  stopAutoplay
} = useSwiper([Navigation, Pagination, Autoplay])

// 模块配置
const modules = [Navigation, Pagination, Autoplay]

// Swiper 初始化回调
const onSwiperReady = (swiper: any) => {
  emit('swiperReady', swiper)
}

// 滑动变化回调
const onSlideChange = (swiper: any) => {
  emit('slideChange', swiper.activeIndex)
}

// 暴露方法给父组件
defineExpose({
  swiperInstance,
  currentSlide,
  isReady,
  goToSlide,
  startAutoplay,
  stopAutoplay
})
</script>

<style lang="less" scoped>
.horizontal-swiper-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.horizontal-swiper {
  width: 100%;
  height: 100%;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);

  &.fullscreen {
    border-radius: 0;
    box-shadow: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10;
  }
}

// 全屏模式下的特殊样式
.horizontal-swiper.fullscreen {

  :deep(.swiper-button-next),
  :deep(.swiper-button-prev) {
    color: white;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    z-index: 50;

    &:after {
      font-size: 24px;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.7);
      transform: scale(1.1);
    }
  }

  :deep(.swiper-pagination) {
    bottom: 30px;
    z-index: 50;
  }

  :deep(.swiper-pagination-bullet) {
    background: rgba(255, 255, 255, 0.6);
    opacity: 1;
    width: 12px;
    height: 12px;

    &.swiper-pagination-bullet-active {
      background: white;
      transform: scale(1.2);
    }
  }
}

// 普通模式下的样式
.horizontal-swiper:not(.fullscreen) {

  :deep(.swiper-button-next),
  :deep(.swiper-button-prev) {
    color: white;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    width: 50px;
    height: 50px;

    &:after {
      font-size: 20px;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.5);
    }
  }

  :deep(.swiper-pagination-bullet) {
    background: rgba(255, 255, 255, 0.5);
    opacity: 1;

    &.swiper-pagination-bullet-active {
      background: white;
    }
  }
}
</style>
