# Swiper 组件架构重构文档

## 重构概述

本次重构将原有的 Swiper 组件架构进行了全面优化，减少了代码冗余，提高了可维护性和复用性。

## 新架构

### 1. BaseSwiper 组件 (`BaseSwiper.vue`)
- **作用**: 通用的 Swiper 基础组件，支持所有 Swiper 配置选项
- **特性**:
  - 支持所有 Swiper 模块（Navigation、Pagination、Autoplay 等）
  - 完整的 TypeScript 类型支持
  - 丰富的配置选项和事件系统
  - 自动样式导入

### 2. useSwiper Hook (`useSwiper.ts`)
- **作用**: 简化的通用 Swiper 逻辑管理
- **特性**:
  - 核心实例管理和控制方法
  - 自动清理机制
  - 模块化设计
  - 移除了特定类型的 hooks，使用更通用的方法

### 3. 特定 Swiper 组件

#### VerticalSwiper (`VerticalSwiper.vue`)
- **作用**: 垂直滚屏组件
- **预设配置**:
  - direction: 'vertical'
  - mousewheel: true
  - keyboard: { enabled: true }
  - pagination: { clickable: true }
- **特性**: 包含导航指示器

#### HorizontalSwiper (`HorizontalSwiper.vue`)
- **作用**: 水平轮播组件
- **预设配置**:
  - direction: 'horizontal'
  - autoplay: { delay: 4000, disableOnInteraction: false }
  - navigation: true
  - pagination: { clickable: true }
- **特性**: 支持全屏模式

#### StorySwiper (`StorySwiper.vue`)
- **作用**: 故事展示轮播组件
- **预设配置**:
  - direction: 'horizontal'
  - spaceBetween: 20
  - autoplay: { delay: 3000, disableOnInteraction: false }
- **特性**: 专门用于故事卡片展示

## 使用示例

### 基础使用
```vue
<template>
  <BaseSwiper
    direction="horizontal"
    :autoplay="{ delay: 3000 }"
    :pagination="{ clickable: true }"
    :navigation="true"
  >
    <swiper-slide>Slide 1</swiper-slide>
    <swiper-slide>Slide 2</swiper-slide>
  </BaseSwiper>
</template>
```

### 垂直滚屏
```vue
<template>
  <VerticalSwiper
    :slide-count="7"
    :show-navigation="true"
    @slide-change="handleSlideChange"
  >
    <swiper-slide>Page 1</swiper-slide>
    <swiper-slide>Page 2</swiper-slide>
  </VerticalSwiper>
</template>
```

### 水平轮播
```vue
<template>
  <HorizontalSwiper
    :fullscreen="true"
    @slide-change="handleSlideChange"
  >
    <swiper-slide>Slide 1</swiper-slide>
    <swiper-slide>Slide 2</swiper-slide>
  </HorizontalSwiper>
</template>
```

### 故事轮播
```vue
<template>
  <StorySwiper
    :stories="storyData"
    title="角色传说"
    @slide-change="handleSlideChange"
  />
</template>
```

## 重构优势

1. **代码复用性**: BaseSwiper 可以在任何需要 Swiper 功能的地方使用
2. **可维护性**: 关注点分离，逻辑清晰
3. **类型安全**: 完整的 TypeScript 支持
4. **配置灵活**: 支持所有 Swiper 配置选项
5. **性能优化**: 按需加载模块，自动清理实例

## 兼容性

- ✅ 保持与现有 HomeView.vue 的完全兼容
- ✅ 所有现有功能正常工作
- ✅ 视觉效果和用户体验不变
- ✅ API 接口保持一致

## 技术栈

- Vue 3 Composition API
- TypeScript
- Swiper.js 最新版本
- Less CSS 预处理器
