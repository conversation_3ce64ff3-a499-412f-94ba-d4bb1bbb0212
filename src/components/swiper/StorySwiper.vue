<template>
  <div class="story-swiper-container">
    <h3 v-if="props.title" class="story-title">{{ props.title }}</h3>
    <BaseSwiper direction="horizontal" :slides-per-view="1" :space-between="20" :speed="800"
      :autoplay="{ delay: 3000, disableOnInteraction: false }" :pagination="{ clickable: true }" :navigation="true"
      :modules="modules" custom-class="story-swiper" @swiper-ready="onSwiperReady" @slide-change="onSlideChange">
      <swiper-slide v-for="(story, index) in props.stories" :key="index" class="story-slide">
        <div class="story-card">
          <div class="story-image">
            <img :src="story.image" :alt="story.title" />
          </div>
          <div class="story-info">
            <h4>{{ story.title }}</h4>
            <p>{{ story.description }}</p>
          </div>
        </div>
      </swiper-slide>
    </BaseSwiper>
  </div>
</template>

<script setup lang="ts">
import { SwiperSlide } from 'swiper/vue'
import BaseSwiper from './BaseSwiper.vue'
import { useSwiper } from '@/hooks/useSwiper'
import { Navigation, Pagination, Autoplay } from 'swiper/modules'

interface StoryItem {
  title: string
  description: string
  image: string
}

interface Props {
  stories: StoryItem[]
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: ''
})

const emit = defineEmits<{
  slideChange: [index: number]
  swiperReady: [swiper: any]
}>()

// 使用基础 Swiper Hook
const {
  swiperInstance,
  currentSlide,
  isReady,
  goToSlide,
  startAutoplay,
  stopAutoplay
} = useSwiper([Navigation, Pagination, Autoplay])

// 模块配置
const modules = [Navigation, Pagination, Autoplay]

// Swiper 初始化回调
const onSwiperReady = (swiper: any) => {
  emit('swiperReady', swiper)
}

// 滑动变化回调
const onSlideChange = (swiper: any) => {
  emit('slideChange', swiper.activeIndex)
}

// 暴露方法给父组件
defineExpose({
  swiperInstance,
  currentSlide,
  isReady,
  goToSlide,
  startAutoplay,
  stopAutoplay
})
</script>

<style lang="less" scoped>
.story-swiper-container {
  max-width: 800px;
  width: 90%;
  z-index: 2;
  position: relative;
  margin-top: 120px; // 为顶部Tab按钮留出空间

  @media (max-width: 768px) {
    width: 95%;
    margin-top: 140px; // 移动端为Tab按钮留出更多空间
  }
}

.story-title {
  color: white;
  font-size: 2.5rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  animation: fadeInUp 1s ease-out;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
}

.story-swiper {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.story-slide {
  background: white;
  border-radius: 15px;
  overflow: hidden;
}

.story-card {
  display: flex;
  height: 300px;
  background: white;

  @media (max-width: 768px) {
    flex-direction: column;
    height: auto;
  }

  .story-image {
    flex: 1;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  .story-info {
    flex: 1;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;

    h4 {
      font-size: 1.8rem;
      font-weight: bold;
      color: #333;
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.1rem;
      color: #666;
      line-height: 1.6;
    }
  }
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
