<template>
  <div class="vertical-swiper-container">
    <swiper
      :modules="modules"
      :direction="config.direction"
      :slides-per-view="config.slidesPerView"
      :space-between="config.spaceBetween"
      :mouse-wheel="config.mousewheel"
      :keyboard="config.keyboard"
      :pagination="config.pagination"
      :speed="config.speed"
      :class="['vertical-swiper', customClass]"
      @swiper="onSwiperInit"
      @slide-change="onSlideChange"
    >
      <slot />
    </swiper>

    <!-- 导航指示器 -->
    <div v-if="showNavigation" class="navigation-dots">
      <div 
        v-for="(slide, index) in slideCount" 
        :key="index"
        :class="['nav-dot', { active: currentSlide === index }]"
        @click="goToSlide(index)"
      >
        {{ index + 1 }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Swiper } from 'swiper/vue'
import { useVerticalSwiper, type SwiperConfig } from '@/hooks/useSwiper'

// 导入 Swiper 样式
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

interface Props {
  slideCount: number
  customConfig?: Partial<SwiperConfig>
  customClass?: string
  showNavigation?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  slideCount: 1,
  customClass: '',
  showNavigation: true
})

interface Emits {
  slideChange: [index: number]
  swiperReady: [swiper: any]
}

const emit = defineEmits<Emits>()

// 使用垂直滚屏 Hook
const {
  swiperInstance,
  currentSlide,
  isReady,
  onSwiperInit: handleSwiperInit,
  onSlideChange: handleSlideChange,
  goToSlide,
  config,
  modules
} = useVerticalSwiper(props.customConfig)

// Swiper 初始化回调
const onSwiperInit = (swiper: any) => {
  handleSwiperInit(swiper)
  emit('swiperReady', swiper)
}

// 滑动变化回调
const onSlideChange = (swiper: any) => {
  handleSlideChange(swiper)
  emit('slideChange', swiper.activeIndex)
}

// 暴露方法给父组件
defineExpose({
  swiperInstance,
  currentSlide,
  isReady,
  goToSlide
})
</script>

<style lang="less" scoped>
.vertical-swiper-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.vertical-swiper {
  width: 100%;
  height: 100vh;
}

// 导航指示器样式
.navigation-dots {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .nav-dot {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.5);
      transform: scale(1.1);
    }

    &.active {
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      border-color: white;
      transform: scale(1.2);
    }
  }

  @media (max-width: 768px) {
    right: 15px;
    
    .nav-dot {
      width: 40px;
      height: 40px;
      font-size: 0.9rem;
    }
  }
}

// Swiper 自定义样式
:deep(.swiper-pagination-bullet) {
  background: rgba(255, 255, 255, 0.5);
  opacity: 1;
  
  &.swiper-pagination-bullet-active {
    background: white;
  }
}

:deep(.swiper-button-next),
:deep(.swiper-button-prev) {
  color: white;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  
  &:after {
    font-size: 20px;
  }
  
  &:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}
</style>
