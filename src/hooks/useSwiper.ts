import { ref, onUnmounted, type Ref } from "vue";
import type { Swiper as SwiperType } from "swiper";
import { Navigation, Pagination, Mousewheel, Keyboard, Autoplay } from "swiper/modules";

// Swiper 配置接口
export interface SwiperConfig {
  direction?: "horizontal" | "vertical";
  slidesPerView?: number;
  spaceBetween?: number;
  speed?: number;
  loop?: boolean;
  autoplay?:
    | {
        delay: number;
        disableOnInteraction?: boolean;
      }
    | boolean;
  mousewheel?: boolean;
  keyboard?: boolean | { enabled: boolean };
  pagination?: boolean | { clickable: boolean };
  navigation?: boolean;
  modules?: any[];
}

// 默认配置
const defaultVerticalConfig: SwiperConfig = {
  direction: "vertical",
  slidesPerView: 1,
  spaceBetween: 0,
  speed: 800,
  mousewheel: true,
  keyboard: { enabled: true },
  pagination: { clickable: true },
  modules: [Navigation, Pagination, Mousewheel, Keyboard],
};

const defaultHorizontalConfig: SwiperConfig = {
  direction: "horizontal",
  slidesPerView: 1,
  spaceBetween: 0,
  speed: 1000,
  autoplay: { delay: 4000, disableOnInteraction: false },
  pagination: { clickable: true },
  navigation: true,
  modules: [Navigation, Pagination, Autoplay],
};

// 主要的 Swiper Hook
export function useSwiper(customConfig?: Partial<SwiperConfig>) {
  const swiperInstance = ref<SwiperType | null>(null);
  const currentSlide = ref(0);
  const isReady = ref(false);

  // 合并配置
  const mergeConfig = (defaultConfig: SwiperConfig, custom?: Partial<SwiperConfig>): SwiperConfig => {
    return { ...defaultConfig, ...custom };
  };

  // 初始化 Swiper 实例
  const onSwiperInit = (swiper: SwiperType) => {
    swiperInstance.value = swiper;
    isReady.value = true;
    currentSlide.value = swiper.activeIndex;
  };

  // 滑动变化回调
  const onSlideChange = (swiper: SwiperType) => {
    currentSlide.value = swiper.activeIndex;
  };

  // 导航到指定滑块
  const goToSlide = (index: number, speed?: number) => {
    if (swiperInstance.value && isReady.value) {
      swiperInstance.value.slideTo(index, speed);
    }
  };

  // 下一张
  const slideNext = () => {
    if (swiperInstance.value && isReady.value) {
      swiperInstance.value.slideNext();
    }
  };

  // 上一张
  const slidePrev = () => {
    if (swiperInstance.value && isReady.value) {
      swiperInstance.value.slidePrev();
    }
  };

  // 开始自动播放
  const startAutoplay = () => {
    if (swiperInstance.value?.autoplay) {
      swiperInstance.value.autoplay.start();
    }
  };

  // 停止自动播放
  const stopAutoplay = () => {
    if (swiperInstance.value?.autoplay) {
      swiperInstance.value.autoplay.stop();
    }
  };

  // 销毁实例
  const destroySwiper = () => {
    if (swiperInstance.value) {
      swiperInstance.value.destroy();
      swiperInstance.value = null;
      isReady.value = false;
    }
  };

  // 组件卸载时清理
  onUnmounted(() => {
    destroySwiper();
  });

  return {
    swiperInstance: swiperInstance as Ref<SwiperType | null>,
    currentSlide,
    isReady,
    onSwiperInit,
    onSlideChange,
    goToSlide,
    slideNext,
    slidePrev,
    startAutoplay,
    stopAutoplay,
    destroySwiper,
    mergeConfig,
  };
}

// 垂直滚屏专用 Hook
export function useVerticalSwiper(customConfig?: Partial<SwiperConfig>) {
  const config = { ...defaultVerticalConfig, ...customConfig };
  const swiper = useSwiper(config);

  return {
    ...swiper,
    config,
    modules: config.modules || defaultVerticalConfig.modules,
  };
}

// 水平轮播专用 Hook
export function useHorizontalSwiper(customConfig?: Partial<SwiperConfig>) {
  const config = { ...defaultHorizontalConfig, ...customConfig };
  const swiper = useSwiper(config);

  return {
    ...swiper,
    config,
    modules: config.modules || defaultHorizontalConfig.modules,
  };
}

// 故事轮播专用 Hook（用于第7屏的故事模块）
export function useStorySwiper(customConfig?: Partial<SwiperConfig>) {
  const storyConfig: SwiperConfig = {
    direction: "horizontal",
    slidesPerView: 1,
    spaceBetween: 20,
    speed: 800,
    autoplay: { delay: 3000, disableOnInteraction: false },
    pagination: { clickable: true },
    navigation: true,
    modules: [Navigation, Pagination, Autoplay],
  };

  const config = { ...storyConfig, ...customConfig };
  const swiper = useSwiper(config);

  return {
    ...swiper,
    config,
    modules: config.modules || storyConfig.modules,
  };
}
