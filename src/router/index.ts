import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory("/"),
  routes: [
    {
      path: "/",
      component: () => import("../views/Official.vue"),
    },
    {
      path: "/pre/:recommend_utm?",
      name: "pre",
      component: () => import("../views/HomeView.vue"),
    },
    {
      path: "/pre2/:recommend_utm?",
      name: "pre2",
      component: () => import("../views/HomeView.vue"),
    },
    {
      path: "/pre3/:recommend_utm?",
      name: "pre3",
      component: () => import("../views/HomeView.vue"),
    },
    {
      path: "/shorts1/:recommend_utm?",
      name: "shorts1",
      component: () => import("../views/shorts1/index.vue"),
    },
    {
      path: "/shorts2/:recommend_utm?",
      name: "shorts2",
      component: () => import("../views/shorts2/index.vue"),
    },
    {
      path: "/shorts3/:recommend_utm?",
      name: "shorts3",
      component: () => import("../views/shorts3/index.vue"),
    },
    {
      path: "/shorts4/:recommend_utm?",
      name: "shorts4",
      component: () => import("../views/shorts4/index.vue"),
    },
  ],
});

export default router;
