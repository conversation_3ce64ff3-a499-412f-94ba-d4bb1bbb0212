import AppApi, { type ToOrderParams } from "@/api/AppApi";
import { ApiResultCode } from "@/api/viewModel/IApiResultCode";
import DateUtils from "@/utils/DateUtils";
import { defineStore } from "pinia";
import { onMounted, ref } from "vue";

// 导入WarmPageConfig类型
interface WarmPageConfig {
  id: number;
  name: string;
  game_id: number;
  order_user_distinct: number;
  start_time: number;
  end_time: number;
  package_config: string;
  appstore_url: string;
  google_play_url: string;
  share_pic_url: string;
  share_title: string;
  video_url: string;
  share_description: string;
  share_name: string;
  assoc_url: string;
  engagement_host: string;
  cutoff_time: number;
  country_code: string;
  is_access: boolean;
  award_time: number;
}

export type EventInit = { request_id: string; visit_id: string };

export type EventParams = {
  utm: string;
  from_page: string;
  sub_page?: string;
  en_name: string;
  request_id?: string;
  visit_id?: string;
  event?: string;
};

export type EventPageInfo = {
  utm: string;
  sub_page?: string;
  from_page: string;
  en_name: string;
};

export const useAppStore = defineStore(
  "app",
  () => {
    // state
    const isDev = ref(import.meta.env.DEV);
    const warmPageConfig = ref<WarmPageConfig>();
    const isShowLoadding = ref(false);
    const eventInitData = ref<EventInit>();
    const eventPageInfo = ref<EventPageInfo>();
    const isClickFbLike = ref(false);
    const isClickDc = ref(false);
    const isFbShare = ref(false);
    const orderPhone = ref("");
    const dialing_code = ref("");
    const token = ref("");
    const isCloseOrderPop = ref(false);
    const isCloseOrderAPop = ref(false);
    const isCloseOrderBPop = ref(false);
    const isCloseOrderCPop = ref(false);
    const isCloseOrderDPop = ref(false);
    const isCloseOrderEPop = ref(false);
    const isShowOrderAPop = ref(false);
    const isShowOrderBPop = ref(false);
    const isShowOrderCPop = ref(false);
    const isShowOrderDPop = ref(false);
    const isShowOrderEPop = ref(false);
    const isShowOrderPop = ref(false);
    const isConfirmRegister = ref(false);
    const isCancelRegister = ref(false);
    const storePopType = ref("");
    const isOver = ref(false);
    const countdown = ref<{
      days: string;
      hours: string;
      minutes: string;
      seconds: string;
    }>();

    // 存储过期时间，用于倒计时计算
    const endTime = ref<number>(0);
    const serverTime = ref<number>(0); // 服务器时间戳
    const clientStartTime = ref<number>(0); // 客户端开始时间
    const countdownTimer = ref<number | null>(null); // 动画帧ID

    // 设置加载状态
    function setLoadding(value: boolean) {
      isShowLoadding.value = value;
    }

    function setEventPageInfo(info: EventPageInfo) {
      eventPageInfo.value = info;
    }

    // 新手机号预约，初始化状态
    function initStatus() {
      isClickFbLike.value = false;
      isClickDc.value = false;
      isFbShare.value = false;
    }

    // 开始倒计时动画
    function startCountdown() {
      if (!endTime.value || !serverTime.value) {
        return;
      }

      // 停止之前的倒计时
      stopCountdown();

      // 记录客户端开始时间
      clientStartTime.value = Date.now();

      // 开始动画循环
      const animate = () => {
        updateCountdownDisplay();
        countdownTimer.value = requestAnimationFrame(animate);
      };

      countdownTimer.value = requestAnimationFrame(animate);
    }

    // 停止倒计时动画
    function stopCountdown() {
      if (countdownTimer.value) {
        cancelAnimationFrame(countdownTimer.value);
        countdownTimer.value = null;
      }
    }

    // 更新倒计时显示
    function updateCountdownDisplay() {
      if (!endTime.value || !serverTime.value || !clientStartTime.value) {
        return;
      }

      // 计算当前服务器时间（服务器时间 + 客户端经过的时间）
      const clientElapsed = Math.floor(
        (Date.now() - clientStartTime.value) / 1000
      );
      const currentServerTime = serverTime.value + clientElapsed;

      // 重新计算倒计时
      const newCountdown = DateUtils.calculateCountdown(
        endTime.value,
        currentServerTime
      );

      if (newCountdown) {
        countdown.value = newCountdown;
        isOver.value = false;
      } else {
        // 倒计时结束，停止动画
        countdown.value = undefined;
        isOver.value = true;
        stopCountdown();
      }
    }

    // 手动更新倒计时（基于传入的服务器时间戳）
    function updateCountdown(currentServerTime?: number) {
      if (!endTime.value) {
        return;
      }

      if (currentServerTime) {
        // 更新服务器时间基准
        serverTime.value = currentServerTime;
        clientStartTime.value = Date.now();

        // 立即更新显示
        updateCountdownDisplay();
      } else {
        // 如果没有传入服务器时间，只更新显示
        updateCountdownDisplay();
      }
    }

    // 获取预热页配置
    async function getWarmPageConfig(params: any) {
      const apiResult = await AppApi.getWarmPage(params);
      if (apiResult.code != ApiResultCode.OK) {
        alert("error!");
        return;
      }

      //判断活动过期时间是否在7天以内，是的话设置天、时、分秒数据
      countdown.value = DateUtils.calculateCountdown(
        apiResult.data?.end_time!,
        apiResult.ts
      );
      if (countdown.value) {
        // 记录过期时间
        endTime.value = apiResult.data?.end_time!;

        // 记录服务器时间并开始倒计时
        serverTime.value = apiResult.ts;
        startCountdown();
      }

      //活动是否过期
      if (apiResult.data?.end_time! <= apiResult.ts) {
        isOver.value = true;
      } else {
        isOver.value = false;
      }
      warmPageConfig.value = apiResult.data;
    }

    // 获取埋点初始化
    async function getEventInit(params: any) {
      const obj = {
        ...eventPageInfo.value,
        request_id: eventInitData.value?.request_id,
        visit_id: eventInitData.value?.visit_id,
        ...params,
      };
      const apiResult = await AppApi.getEventInit(obj);
      if (apiResult.code != ApiResultCode.OK) {
        alert("error!");
        return;
      }
      eventInitData.value = apiResult.data;
    }

    // 统计后台埋点接口
    async function reportEvent(eventName: string) {
      const params = {
        ...eventPageInfo.value,
        event: eventName,
        request_id: eventInitData.value?.request_id,
        visit_id: eventInitData.value?.visit_id,
      };
      const apiResult = await AppApi.reportEvent(params);
      if (apiResult.code != ApiResultCode.OK) {
        // alert("error!");
        return;
      }
    }

    // 下单逻辑
    async function toOrder(params: ToOrderParams, isShowLoadding = true) {
      try {
        // 设置加载状态为 true
        if (isShowLoadding) {
          setLoadding(true);
        }

        // 调用下单 API
        const apiResult = await AppApi.toOrder(params);

        // 取消加载状态
        if (isShowLoadding) {
          setLoadding(false);
        }

        if (!apiResult.data || apiResult.code !== ApiResultCode.OK) {
          return apiResult;
        }
        return apiResult;
      } catch (error) {
        console.error("toOrder Error:", error);
        if (isShowLoadding) {
          setLoadding(false);
        }
        alert("An error occurred while placing the order.");
        throw error;
      }
    }

    return {
      warmPageConfig,
      getWarmPageConfig,
      toOrder,
      isShowLoadding,
      setLoadding,
      getEventInit,
      eventInitData,
      reportEvent,
      eventPageInfo,
      setEventPageInfo,
      isClickFbLike,
      isClickDc,
      isFbShare,
      orderPhone,
      dialing_code,
      initStatus,
      token,
      isCloseOrderPop,
      isCloseOrderAPop,
      isCloseOrderBPop,
      isShowOrderAPop,
      isShowOrderBPop,
      isShowOrderCPop,
      isShowOrderDPop,
      isShowOrderEPop,
      isCloseOrderCPop,
      isCloseOrderDPop,
      isCloseOrderEPop,
      isShowOrderPop,
      storePopType,
      isConfirmRegister,
      isCancelRegister,
      isOver,
      countdown,
      endTime,
      updateCountdown,
      startCountdown,
      stopCountdown,
      isDev,
    };
  },
  {
    persist: {
      key: "app_store_2025-07-01", // 自定义存储名称
      storage: localStorage, // 使用 localStorage 或 sessionStorage
      omit: [
        "isDev",
        "countdown",
        "endTime",
        "serverTime",
        "clientStartTime",
        "countdownTimer",
        "isOver",
      ], // 排除倒计时相关字段
    },
  }
);
