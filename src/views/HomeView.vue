<template>
  <div class="home-view">
    <!-- 主要垂直滚屏容器 -->
    <swiper :modules="modules" direction="vertical" :slides-per-view="1" :space-between="0" :mouse-wheel="true"
      :keyboard="{ enabled: true }" :pagination="{ clickable: true }" :speed="800" class="main-swiper"
      @swiper="onMainSwiperInit" @slide-change="onSlideChange">
      <!-- 第1屏 - 首页 -->
      <swiper-slide class="slide-item slide-1">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第2屏 - 预约 -->
      <swiper-slide class="slide-item slide-2">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第3屏 - 抽奖 -->
      <swiper-slide class="slide-item slide-3">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第4屏 - 里程碑 -->
      <swiper-slide class="slide-item slide-4">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第5屏 - 职业介绍 -->
      <swiper-slide class="slide-item slide-5">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第6屏 - 公会 -->
      <swiper-slide class="slide-item slide-6">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第7屏 - 游戏世界观介绍 -->
      <swiper-slide class="slide-item slide-7">
        <div class="slide-content">
          <div class="world-view-container">

            <!-- Tab 切换导航 -->
            <div class="tab-navigation">
              <button :class="['tab-btn', { active: activeTab === 'tab1' }]" @click="switchTab('tab1')">
                世界探索
              </button>
              <button :class="['tab-btn', { active: activeTab === 'tab2' }]" @click="switchTab('tab2')">
                角色故事
              </button>
            </div>

            <!-- Tab 内容区域 -->
            <div class="tab-content">
              <!-- Tab1: 水平全屏轮播 -->
              <div v-show="activeTab === 'tab1'" class="tab-panel tab1-panel">
                <swiper :modules="horizontalModules" direction="horizontal" :slides-per-view="1" :space-between="0"
                  :autoplay="{ delay: 4000, disableOnInteraction: false }" :navigation="true" :speed="1000" :loop="true"
                  class="horizontal-swiper" @swiper="onHorizontalSwiperInit">
                  <swiper-slide v-for="(image, index) in worldImages" :key="index" class="horizontal-slide"
                    :style="{ backgroundImage: `url(${image})` }">
                    <div class="horizontal-slide-content">
                    </div>
                  </swiper-slide>
                </swiper>
              </div>

              <!-- Tab2: 固定背景 + 居中轮播 -->
              <div v-show="activeTab === 'tab2'" class="tab-panel tab2-panel">
                <div class="story-carousel-container">
                  <h3 class="story-title">角色传说</h3>
                  <swiper :modules="storyModules" direction="horizontal" :slides-per-view="1" :space-between="20"
                    :autoplay="{ delay: 3000, disableOnInteraction: false }" :pagination="{ clickable: true }"
                    :navigation="true" class="story-swiper">
                    <swiper-slide v-for="(story, index) in storyData" :key="index" class="story-slide">
                      <div class="story-card">
                        <div class="story-image">
                          <img :src="story.image" :alt="story.title" />
                        </div>
                        <div class="story-info">
                          <h4>{{ story.title }}</h4>
                          <p>{{ story.description }}</p>
                        </div>
                      </div>
                    </swiper-slide>
                  </swiper>
                </div>
              </div>
            </div>
          </div>
        </div>
      </swiper-slide>
    </swiper>

    <!-- 导航指示器 -->
    <div class="navigation-dots">
      <div v-for="(slide, index) in 7" :key="index" :class="['nav-dot', { active: currentSlide === index }]"
        @click="goToSlide(index)">
        {{ index + 1 }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import {
  Navigation,
  Pagination,
  Mousewheel,
  Keyboard,
  Autoplay
} from 'swiper/modules'
import type { Swiper as SwiperType } from 'swiper'

// 导入 Swiper 样式
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

// 导入图片
import p7_1_1 from '@/assets/imgs/bg/p7-1-1.jpg'
import p7_1_2 from '@/assets/imgs/bg/p7-1-2.jpg'
import p7_1_3 from '@/assets/imgs/bg/p7-1-3.jpg'
import p7_1_4 from '@/assets/imgs/bg/p7-1-4.jpg'
import p7_1_5 from '@/assets/imgs/bg/p7-1-5.jpg'

// 世界观图片
const worldImages = [p7_1_1, p7_1_2, p7_1_3, p7_1_4, p7_1_5]

// Swiper 模块配置
const modules = [Navigation, Pagination, Mousewheel, Keyboard]
const horizontalModules = [Navigation, Pagination, Autoplay]
const storyModules = [Navigation, Pagination, Autoplay]

// 响应式数据
const currentSlide = ref(0)
const activeTab = ref('tab1')
const mainSwiper = ref<SwiperType | null>(null)
const horizontalSwiper = ref<SwiperType | null>(null)

// 角色故事数据
const storyData = reactive([
  {
    title: '传奇战士',
    description: '勇敢的战士踏上征程，为了守护家园而战',
    image: p7_1_1
  },
  {
    title: '神秘法师',
    description: '掌握古老魔法的智者，探寻世界的奥秘',
    image: p7_1_2
  },
  {
    title: '敏捷刺客',
    description: '隐藏在暗影中的杀手，执行秘密任务',
    image: p7_1_3
  },
  {
    title: '圣洁牧师',
    description: '治愈伤痛的圣者，为队友提供支援',
    image: p7_1_4
  },
  {
    title: '远程射手',
    description: '精准的弓箭手，百步穿杨的神射手',
    image: p7_1_5
  }
])

// Swiper 实例初始化回调
const onMainSwiperInit = (swiper: SwiperType) => {
  mainSwiper.value = swiper
}

const onHorizontalSwiperInit = (swiper: SwiperType) => {
  horizontalSwiper.value = swiper
}

// 滑动变化回调
const onSlideChange = (swiper: SwiperType) => {
  currentSlide.value = swiper.activeIndex
}

// Tab 切换功能
const switchTab = (tab: string) => {
  activeTab.value = tab
}

// 导航到指定滑块
const goToSlide = (index: number) => {
  if (mainSwiper.value) {
    mainSwiper.value.slideTo(index)
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 可以在这里添加额外的初始化逻辑
})

// 组件卸载时的清理
onUnmounted(() => {
  // 清理 Swiper 实例
  if (mainSwiper.value) {
    mainSwiper.value.destroy()
  }
  if (horizontalSwiper.value) {
    horizontalSwiper.value.destroy()
  }
})
</script>

<style lang="less" scoped>
.home-view {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.main-swiper {
  width: 100%;
  height: 100vh;
}

.slide-item {
  width: 100%;
  height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;

  // 为每个屏幕设置背景图
  &.slide-1 {
    background-image: url('@/assets/imgs/bg/p1.jpg');
  }

  &.slide-2 {
    background-image: url('@/assets/imgs/bg/p2.jpg');
  }

  &.slide-3 {
    background-image: url('@/assets/imgs/bg/p3.jpg');
  }

  &.slide-4 {
    background-image: url('@/assets/imgs/bg/p4.jpg');
  }

  &.slide-5 {
    background-image: url('@/assets/imgs/bg/p5.jpg');
  }

  &.slide-6 {
    background-image: url('@/assets/imgs/bg/p6.jpg');
  }

  &.slide-7 {
    background-color: #000;
  }
}

.slide-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;

  .slide-title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    animation: fadeInUp 1s ease-out;

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }
  }

  .slide-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
    animation: fadeInUp 1s ease-out 0.3s both;

    @media (max-width: 768px) {
      font-size: 1.2rem;
    }
  }

  .cta-button {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.2rem;
    font-weight: bold;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: fadeInUp 1s ease-out 0.6s both;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 25px rgba(255, 107, 107, 0.4);
    }

    &:active {
      transform: translateY(-1px);
    }
  }
}

// 第7屏特殊样式
.world-view-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 0;
  box-sizing: border-box;
  position: relative;
}

.tab-navigation {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
  z-index: 100;
  position: absolute;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);

  .tab-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    font-weight: 500;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }

    &.active {
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      border-color: white;
    }
  }
}

.tab-content {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.tab-panel {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

// Tab1 - 水平全屏轮播样式
.tab1-panel {
  // 确保Tab1面板占据整个第7屏
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10;

  .horizontal-swiper {
    width: 100%;
    height: 100%;
    border-radius: 0; // 移除圆角以实现真正全屏
    overflow: hidden;
    box-shadow: none; // 移除阴影以实现真正全屏
  }

  .horizontal-slide {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100vw;
    height: 100vh;
  }

  .horizontal-slide-content {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
    margin-top: 100px; // 为顶部Tab按钮留出空间

    h3 {
      font-size: 3rem;
      font-weight: bold;
      margin-bottom: 1rem;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
      animation: fadeInUp 1s ease-out;
    }

    p {
      font-size: 1.5rem;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
      animation: fadeInUp 1s ease-out 0.3s both;
    }
  }

  // 自定义Tab1中的Swiper导航按钮样式
  :deep(.swiper-button-next),
  :deep(.swiper-button-prev) {
    color: white;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    z-index: 50;

    &:after {
      font-size: 24px;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.7);
      transform: scale(1.1);
    }
  }

  // 自定义Tab1中的分页器样式
  :deep(.swiper-pagination) {
    bottom: 30px;
    z-index: 50;
  }

  :deep(.swiper-pagination-bullet) {
    background: rgba(255, 255, 255, 0.6);
    opacity: 1;
    width: 12px;
    height: 12px;

    &.swiper-pagination-bullet-active {
      background: white;
      transform: scale(1.2);
    }
  }
}

// Tab2 - 固定背景 + 居中轮播样式
.tab2-panel {
  background-image: url('@/assets/imgs/bg/p7-2.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 10;
}

.story-carousel-container {
  width: 90%;
  z-index: 2;
  position: relative;
  margin-top: 120px; // 为顶部Tab按钮留出空间

  .story-title {
    color: white;
    font-size: 2.5rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    animation: fadeInUp 1s ease-out;
  }
}

.story-swiper {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.story-slide {
  background: white;
  border-radius: 15px;
  overflow: hidden;
}

.story-card {
  display: flex;
  height: 300px;
  background: white;

  @media (max-width: 768px) {
    flex-direction: column;
    height: auto;
  }

  .story-image {
    flex: 1;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  .story-info {
    flex: 1;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;

    h4 {
      font-size: 1.8rem;
      font-weight: bold;
      color: #333;
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.1rem;
      color: #666;
      line-height: 1.6;
    }
  }
}

// 导航指示器样式
.navigation-dots {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .nav-dot {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.5);
      transform: scale(1.1);
    }

    &.active {
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      border-color: white;
      transform: scale(1.2);
    }
  }

  @media (max-width: 768px) {
    right: 15px;

    .nav-dot {
      width: 40px;
      height: 40px;
      font-size: 0.9rem;
    }
  }
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Swiper 自定义样式
:deep(.swiper-pagination-bullet) {
  background: rgba(255, 255, 255, 0.5);
  opacity: 1;

  &.swiper-pagination-bullet-active {
    background: white;
  }
}

:deep(.swiper-button-next),
:deep(.swiper-button-prev) {
  color: white;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 50px;
  height: 50px;

  &:after {
    font-size: 20px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .world-view-container {
    padding: 0;
  }

  .tab-navigation {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    top: 1rem;

    .tab-btn {
      width: 200px;
      text-align: center;
      padding: 10px 25px;
      font-size: 1rem;
    }
  }

  .tab1-panel {
    .horizontal-slide-content {
      margin-top: 140px; // 移动端为Tab按钮留出更多空间

      h3 {
        font-size: 2.2rem;
      }

      p {
        font-size: 1.2rem;
      }
    }
  }

  .story-carousel-container {
    width: 95%;
    margin-top: 140px; // 移动端为Tab按钮留出更多空间

    .story-title {
      font-size: 2rem;
    }
  }
}

@media (max-width: 480px) {
  .slide-content {
    .slide-title {
      font-size: 2rem;
    }

    .slide-subtitle {
      font-size: 1rem;
    }

    .cta-button {
      padding: 12px 30px;
      font-size: 1rem;
    }
  }

  .horizontal-slide-content {
    h3 {
      font-size: 2rem;
    }

    p {
      font-size: 1.1rem;
    }
  }
}
</style>