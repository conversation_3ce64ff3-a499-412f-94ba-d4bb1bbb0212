<script setup lang="ts">
import { useResizeHandler } from "@/hooks/useResizeHandler";
import { useCountdown } from "@/hooks/useCountdown";
import {
  onMounted,
  onUnmounted,
  reactive,
  ref,
  nextTick,
  computed,
  watch,
} from "vue";
import WebM from "@/components/WebM.vue";
import test from '@/assets/imgs/1.webm'
</script>

<template>
  <div class="main">
    <!-- <WebM class="test" :src="test"></WebM> -->
  </div>
</template>

<style scoped lang="less">
@import url("./mobile.less");

/* 确保popupCommon的层级最高 */
:deep(.popup-overlay:has(.popup-common)) {
  z-index: 99998 !important;
}
</style>
