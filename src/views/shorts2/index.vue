<script setup lang="ts">
import AppApi from "@/api/AppApi";
import ClearDataButton from "@/components/ClearDataButton.vue";
import { areaConfig, areaIndexs, packageType } from "@/config/appConfig";
import { eventConfig, platformMap, utmMap } from "@/config/platformConfig";
import { useAnalytics } from "@/hooks/useAnalytics";
import { usePageLeaveTracker } from "@/hooks/usePageLeaveTracker";
import { useResizeHandler } from "@/hooks/useResizeHandler";
import router from "@/router";
import { useAppStore } from "@/stores/app";
import AppUtils from "@/utils/AppUtils";
import { storeToRefs } from "pinia";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import * as p from "platform";
import { useUrlParams } from "@/hooks/useUrlParams";
import Popup from "@/components/Popup.vue";
import { ApiResultCode } from "@/api/viewModel/IApiResultCode";
import { usePreloadAssets } from "@/hooks/usePreloadAssets";

const en_name = "YBLBTW";
const iosUrl = "https://apps.apple.com/app/id6746176096";
const gpUrl = "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat";
const protocolUrl = "https://webstatis.knetgame.com/yblbtw_protocal.html";
const privacyUrl = "https://webstatis.knetgame.com/yblbtw_privacy.html";
const contactUsLink = "https://www.facebook.com/profile.php?id=61576604522344";
const fbLikeId = "61576604522344";
const fbLikeUrl = "fb://page/61576604522344";
const platform: any = AppUtils.isIOS() ? "ios" : "gp";

//预加载资源 - 只加载 shorts2 目录下的资源
const shorts1Assets = Object.values(
  import.meta.glob("/src/assets/imgs/shorts2/**/*.{jpg,png,svg,mp4,webp}")
);
usePreloadAssets(shorts1Assets as any);

useResizeHandler();
const { trackEvent, getChannelConfig, ga4TrackEvent } =
  useAnalytics(platformMap);
const appStore = useAppStore();
const { warmPageConfig, orderPhone, dialing_code } = storeToRefs(appStore);

const route = useRoute();
const { recommend_code, recommend_utm } = useUrlParams();

const from_page = ref((route.name as string) || "shorts2");
const openArea = ref(false);
const areaRef = ref<HTMLElement>();
const phone = ref(orderPhone.value);
const hasFocus = ref(false);
const areaCode = ref<any>(dialing_code.value || "886");
const orderResult = ref<any>();
const grantPackages = ref<any>({});
const commonMsg = ref("");
const phoneError = ref(false);
const phoneErrorMsg = ref("請輸入正確的手機號碼");
const isOnToOrder = ref(false);
const agree = ref(true);

const apiCommonParams = (): any => {
  const routeName = router.currentRoute.value.name;
  const params = {
    platform: p.os?.family, // 平台 windows, mac, android
    os_ver: p.os?.version, // 系统版本
    browser_engine: p.name, //   浏览器引擎 chrome,xxx
    browser_ver: p.version, // 浏览器版本
    Language: "TW",
    version: 1,
    en_name,
    page: routeName,
    phone: phone.value || undefined,
    dialing_code: areaCode.value || undefined,
    token: orderResult.value?.token || undefined,
    code: recommend_code.value || undefined,
    recommend_utm: recommend_utm.value || "other",
  };
  return params;
};
usePageLeaveTracker(apiCommonParams());

//弹窗
const popupCommon = ref<InstanceType<typeof Popup> | null>(null);
const popupOrderAfter = ref<InstanceType<typeof Popup> | null>(null);
const popupOrderStoreAfter = ref<InstanceType<typeof Popup> | null>(null);

const selectAreaName = computed(() => {
  return `${areaConfig[areaCode.value].name}+${areaCode.value}`;
});

const storeClickEvent = () => {
  if (recommend_utm.value) {
    const channelConfig = getChannelConfig();
    if (channelConfig) {
      const type = utmMap[recommend_utm.value.toLowerCase()];
      const storeClick = channelConfig.storeClick;
      if (storeClick) {
        const eventName = storeClick[platform].eventName;
        const params = storeClick[platform].params;
        trackEvent(type, eventName, params);
      }
    }
  }
};

const toLink = (code: string, isMutual = true) => {
  // 检测是否在 Facebook WebView 中
  const isFacebookWebView = /FBAN|FBAV/i.test(navigator.userAgent);
  const channelConfig = getChannelConfig();
  switch (code) {
    case "ios":
      storeClickEvent();
      const url = channelConfig ? channelConfig.ios_cpp_link : iosUrl;
      if (isFacebookWebView) {
        location.href = url as string;
      } else {
        window.open(url, "_blank");
      }
      break;
    case "gp":
      storeClickEvent();
      if (channelConfig) {
        window.open(channelConfig.gp_link);
      } else {
        window.open(gpUrl);
      }
      break;
    case "protocol":
      window.open(protocolUrl);
      break;
    case "privacy":
      window.open(privacyUrl);
      break;
    case "contactUs":
      window.open(contactUsLink);
      break;
    case "fbLike":
      // PC端跳转到Facebook页面
      if (isFacebookWebView) {
        if (platform == "ios") {
          const url = `fb://profile?id=${fbLikeId}`;
          window.open(url);
        } else {
          window.open(fbLikeUrl);
        }
      } else {
        window.open(contactUsLink);
      }
      break;
  }
};

const onInputFocus = () => {
  hasFocus.value = true;
};

const onInputBlur = () => {
  hasFocus.value = false;
};

const package_config = computed(() => {
  if (!warmPageConfig.value) {
    return {};
  }
  return JSON.parse(warmPageConfig.value?.package_config);
});

const grant = async (type: string) => {
  if (!orderPhone.value || !orderResult.value) {
    return;
  }
  //判断数组中是否存在name等于type的数据
  const index = grantPackages.value.findIndex((c: any) => c.name == type);
  if (index > -1) {
    return;
  }
  await AppApi.grant({
    package_unique_id: package_config.value[type].package_num,
    ...apiCommonParams(),
  });
  toOrder(true);
};

// 展示消息弹窗
const showMessagePopup = (msg: string = "") => {
  commonMsg.value = msg;
  popupCommon.value?.open();
};

const toOrder = async (isLoggedIn = false, isPop = false, popTyep = "") => {
  if (!agree.value) {
    return showMessagePopup("同意個人資料收集使用及授權獎勵簡訊");
  }
  if (orderPhone.value && !isLoggedIn) {
    return showMessagePopup("您已成功預約請勿重複操作");
  }
  if (!phone.value || phoneError.value) {
    return showMessagePopup(phoneErrorMsg.value);
  }

  isOnToOrder.value = true;

  try {
    const apiResult = await appStore.toOrder(
      {
        page: from_page.value,
        recommend_utm,
        ...apiCommonParams(),
      },
      !isLoggedIn
    );
    if (apiResult.code != ApiResultCode.OK) {
      return alert(apiResult.message);
    }
    if (apiResult.code === ApiResultCode.OK) {
      orderResult.value = apiResult.data;
      grantPackages.value = apiResult.data?.packages;
      orderPhone.value = phone.value;
      dialing_code.value = areaCode.value;

      //新用户预约，重置状态
      if (apiResult.data?.is_new) {
        appStore.initStatus();
        grant(packageType.yblb_order_iphone16);
        grant(packageType.yblb_order_lemon);
      }

      //首次登录
      if (!isLoggedIn) {
        //上报后台事件，跟跳出率相关
        appStore.reportEvent("active");
        ga4TrackEvent(eventConfig.register_all);
        ga4TrackEvent(eventConfig.register_shorts);
        popupOrderAfter.value?.open();
        if (recommend_utm.value) {
          if (recommend_utm.value) {
            const channelConfig = getChannelConfig();
            if (channelConfig) {
              const type = utmMap[recommend_utm.value.toLowerCase()];
              trackEvent(type, channelConfig.eventName, channelConfig.params);
            }
          }
        }
      }
    }

    isOnToOrder.value = false;
  } catch (error) {
    isOnToOrder.value = false;
  }
};

// 添加点击外部区域关闭下拉菜单的功能
const handleClickOutside = (event: MouseEvent) => {
  // 如果下拉菜单没有打开，直接返回
  if (!openArea.value) {
    return;
  }
  const target = event.target as HTMLElement;
  // 检查是否点击在area相关元素内
  if (areaRef.value && areaRef.value.contains(target)) {
    return;
  }
  // 如果点击在外部，关闭下拉菜单
  openArea.value = false;
};

const setEventPageInfo = () => {
  const eventParams = {
    utm: recommend_utm.value,
    sub_page: (route.params?.subpage as string) || undefined,
    from_page: from_page.value,
    en_name,
  };
  appStore.setEventPageInfo(eventParams);
};

//监听
watch(phone, (newPhone) => {
  phoneError.value = !areaConfig[areaCode.value].reg.test(newPhone);
});

// 监听地区切换，重新校验手机号
watch(areaCode, () => {
  if (phone.value) {
    phoneError.value = !areaConfig[areaCode.value].reg.test(phone.value);
  }
});

onMounted(() => {
  setEventPageInfo();
  appStore
    .getWarmPageConfig({
      en_name,
      ...apiCommonParams(),
    })
    .then(() => {
      //没有登录情况
      if (warmPageConfig.value && !orderPhone.value) {
        //根据服务端返回的ISOCODE展示对应的区号 //HK香港 SG新加坡 MO澳门 TW台湾  MY马来西亚
        switch (warmPageConfig.value.country_code) {
          case "TW":
            areaCode.value = "886";
            break;
          case "HK":
            areaCode.value = "852";
            break;
          case "MO":
            areaCode.value = "853";
            break;
          case "SG":
            areaCode.value = "65";
            break;
          case "MY":
            areaCode.value = "60";
            break;
        }
      }
    });

  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>

<template>
  <div class="main">
    <div class="p1">
      <div class="phone-div">
        <div class="area" ref="areaRef">
          <div class="area-select">
            <span
              class="area-select-text"
              :class="{
                'small-60': areaCode == '60',
                'small-65': areaCode == '65',
              }"
              @click="openArea = !openArea"
              >{{ selectAreaName }}</span
            >
          </div>
          <div class="area-options" :class="{ active: openArea }">
            <div
              class="area-option"
              v-for="key in areaIndexs"
              :key="areaConfig[key].code"
              @click="
                areaCode = key;
                openArea = false;
              "
            >
              {{ areaConfig[key].name }}+{{ key }}
            </div>
          </div>
        </div>
        <div class="input-box">
          <input
            :disabled="!!orderPhone"
            v-model="phone"
            type="tel"
            :maxlength="areaConfig[areaCode].maxLength"
            :placeholder="areaConfig[areaCode].placeholder"
            :class="{ error: phoneError }"
            @focus="onInputFocus"
            @blur="onInputBlur"
          />
        </div>
      </div>
      <div
        class="pre-btn pointer"
        :class="{ animation: !orderPhone }"
        @click="toOrder(false)"
      ></div>

      <input type="checkbox" class="agree" v-model="agree" :checked="agree" />

      <div class="protocol pointer" @click="toLink('protocol')"></div>
      <div class="privacy pointer" @click="toLink('privacy')"></div>
      <div class="contact-us pointer" @click="toLink('contactUs')"></div>
    </div>
    <ClearDataButton />

    <Popup ref="popupCommon">
      <div class="popup-common">
        <div class="close"></div>
        <div class="title">提示</div>
        <div class="desc">{{ commonMsg }}</div>
      </div>
    </Popup>

    <Popup ref="popupOrderAfter">
      <div class="popup-order-after" :class="platform">
        <div class="close"></div>
        <div
          class="store-btn animation"
          :class="[platform]"
          @click="
            ga4TrackEvent(eventConfig.click_appstore_all);
            ga4TrackEvent(
              platform == 'ios'
                ? eventConfig.click_ios_shorts
                : eventConfig.click_gp_shorts
            );
            ga4TrackEvent(
              platform == 'ios'
                ? eventConfig.click_ios_all
                : eventConfig.click_gp_all
            );
            toLink(platform);
            popupOrderAfter?.close();
            popupOrderStoreAfter?.open();
          "
        ></div>
      </div>
    </Popup>

    <Popup ref="popupOrderStoreAfter">
      <div class="popup-order-store-after" :class="platform">
        <div class="close"></div>
        <div
          class="btn animation"
          :class="[platform]"
          @click="popupOrderStoreAfter?.close()"
        ></div>
      </div>
    </Popup>
  </div>
</template>

<style scoped lang="less">
.main {
  position: relative;
  background: url("@/assets/imgs/shorts2/bg.jpg") no-repeat;
  background-size: 100% 100%;
  width: 7.5rem;
  height: 16.91rem;
  margin: 0 auto;
  .p1 {
    .phone-div {
      position: absolute;
      display: flex;
      top: 10rem;
      left: 1.03rem;
      height: 0.48rem;
      line-height: 0.48rem;
      .area {
        position: relative;
        font-size: 0.21rem;
        color: #9b7f54;
        font-weight: bold;
        .area-options {
          position: absolute;
          top: 0.6rem;
          left: -0.5rem;
          display: flex;
          width: 2.5rem;
          height: 4.7rem;
          justify-content: space-evenly;
          flex-direction: column;
          background-color: #f4eddd;
          border: 1px solid white;
          font-size: 0.35rem;
          border-radius: 0.2rem;
          z-index: 2;
          text-align: center;
          display: none;
          &.active {
            display: flex;
          }
        }
        .area-select-text {
          display: flex;
          height: 0.48rem;
          line-height: 0.48rem;
          width: 1.55rem;
          &.small-60 {
            font-size: 0.16rem;
          }
          &.small-65 {
            font-size: 0.2rem;
          }
        }
      }
      .input-box {
        position: absolute;
        top: 0;
        left: 1.21rem;
        width: 2.2rem;
        height: 100%;
        padding: 0.1rem 0;
        line-height: 0.45rem;
        input {
          display: flex;
          font-size: 0.18rem;
          &::placeholder {
            color: #9b7f54;
            font-size: 0.24rem;
            font-weight: bold;
          }
          color: green;
          &.error {
            color: red;
          }
        }
      }
    }
    .pre-btn {
      position: absolute;
      top: 9.1rem;
      left: 4.64rem;
      width: 2.61rem;
      height: 1.95rem;
      background: url("@/assets/imgs/shorts2/btn.png") no-repeat;
      background-size: 100% 100%;
    }
    .agree {
      position: absolute;
      top: 10.63rem;
      left: 1.346rem;
      width: 0.2rem;
      height: 0.2rem;
      appearance: auto;
    }

    .protocol {
      position: absolute;
      top: 16.58rem;
      left: 1.45rem;
      width: 0.6rem;
      height: 0.2rem;
    }
    .privacy {
      position: absolute;
      top: 16.58rem;
      left: 2.02rem;
      width: 0.55rem;
      height: 0.2rem;
    }
    .contact-us {
      position: absolute;
      top: 16.58rem;
      left: 2.58rem;
      width: 0.55rem;
      height: 0.2rem;
    }
  }

  .popup-overlay {
    .close {
      position: absolute;
      top: -0.2rem;
      right: -0.23rem;
      width: 0.27rem;
      height: 0.25rem;
      background: url("@/assets/imgs/pop/close.png") no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
    }
  }

  .popup-common {
    width: 6.63rem;
    height: 3.09rem;
    background: url("@/assets/imgs/pop/common.png") no-repeat;
    background-size: 100% 100%;
    color: #a1684f;
    font-weight: bold;
    .title {
      position: absolute;
      top: 0.64rem;
      left: 2.95rem;
      font-size: 0.46rem;
      line-height: 0.36rem;
      text-transform: uppercase;
      text-align: center;
    }
    .desc {
      width: 5.2rem;
      position: absolute;
      top: 1.5rem;
      left: 50%;
      transform: translateX(-50%);
      font-size: 0.3rem;
      line-height: 0.36rem;
      text-transform: uppercase;
      color: #a1684f;
      text-align: center;
    }
  }

  .popup-order-after {
    width: 7.5rem;
    height: 7.1rem;
    &.ios {
      background: url("@/assets/imgs/shorts2/pop/order-after/ios-pop.png")
        no-repeat;
      background-size: 100% 100%;
    }
    &.gp {
      background: url("@/assets/imgs/shorts2/pop/order-after/gp-pop.png")
        no-repeat;
      background-size: 100% 100%;
    }
    .store-btn {
      position: absolute;
      top: 5.5rem;
      right: 1.1rem;
      width: 2.53rem;
      height: 0.86rem;
      &.ios {
        background: url("@/assets/imgs/shorts2/pop/order-after/ios.png")
          no-repeat;
        background-size: 100% 100%;
      }
      &.gp {
        background: url("@/assets/imgs/shorts2/pop/order-after/gp.png")
          no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .popup-order-store-after {
    width: 7.5rem;
    height: 9.38rem;
    background: url("@/assets/imgs/shorts2/pop/order-store-after/bg.png")
      no-repeat;
    background-size: 100% 100%;
    .close {
      background: none;
    }
    .btn {
      position: absolute;
      top: 6.9rem;
      left: 1.4rem;
      width: 4.47rem;
      height: 1.8rem;
      background: url("@/assets/imgs/shorts2/pop/order-store-after/btn.png")
        no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
