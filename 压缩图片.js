import fs from "fs";
import path from "path";
import sharp from "sharp";

// 目标文件夹路径
const folderPath = "./src/assets/imgs2/";

// 压缩图片的函数
async function compressImage(filePath) {
  try {
    const extension = path.extname(filePath).toLowerCase();

    if (extension === ".png") {
      // 压缩并保留透明度
      await sharp(filePath)
        .png({ quality: 80, compressionLevel: 9 }) // 保持透明度并压缩
        .toFile(filePath + ".tmp"); // 临时文件
    } else if (extension === ".jpg" || extension === ".jpeg") {
      // 压缩 JPEG 文件
      await sharp(filePath)
        .jpeg({ quality: 80 }) // 压缩质量
        .toFile(filePath + ".tmp"); // 临时文件
    }

    // 用压缩后的临时文件替换原文件
    fs.renameSync(filePath + ".tmp", filePath);
    console.log(`压缩完成: ${filePath}`);
  } catch (error) {
    console.error(`压缩失败: ${filePath}`, error);
  }
}

// 递归遍历文件夹并压缩图片
function compressImagesInFolder(folder) {
  fs.readdir(folder, (err, files) => {
    if (err) {
      return console.error("无法读取文件夹:", err);
    }

    files.forEach((file) => {
      const filePath = path.join(folder, file);

      // 检查是否是文件夹
      if (fs.statSync(filePath).isDirectory()) {
        // 递归处理子文件夹
        compressImagesInFolder(filePath);
      } else {
        // 只处理图片文件
        if (
          file.endsWith(".jpg") ||
          file.endsWith(".jpeg") ||
          file.endsWith(".png")
        ) {
          compressImage(filePath);
        }
      }
    });
  });
}

// 开始压缩
compressImagesInFolder(folderPath);
